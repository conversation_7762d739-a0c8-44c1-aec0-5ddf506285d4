name: Deploy to EC2
on:
  push:
    branches: [ main ]
  workflow_dispatch:
jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to EC2
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ubuntu
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        script: |
          # Pull latest code
          cd /home/<USER>/notifly-admin-panel
          git pull https://<EMAIL>/sujata-autviz/notifly-admin-panel.git
          docker-compose down 
          docker-compose up -d --build
          
          
