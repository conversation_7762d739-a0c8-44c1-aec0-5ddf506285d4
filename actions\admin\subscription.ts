"use server";

import { makeActionApiRequest } from "@/lib/common/api-utils";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

interface CreateSubscriptionPayload {
  plan_id: number;
  is_yearly: boolean;
}

export async function createSubscriptionAction(payload: CreateSubscriptionPayload) {
  if (!API_URL) {
    return { success: false, error: "API URL not configured" };
  }

  return await makeActionApiRequest(
    `${API_URL}/api/v1/subscription/`,
    "POST",
    payload
  );
}


