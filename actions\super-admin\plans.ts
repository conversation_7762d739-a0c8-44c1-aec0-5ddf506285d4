"use server";

import { revalidatePath } from "next/cache";
import { RoleTypePlan } from "@/types";
import { createPlanFeaturesAction, updatePlanFeaturesAction } from "./features";
import { makeActionApiRequest } from "@/lib/common/api-utils";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

interface CreatePlanPayload {
  title: string;
  monthly_amount: number;
  yearly_amount: number;
  role_type_plan: RoleTypePlan;
}

export async function createPlanAction(data: CreatePlanPayload) {
  const result = await makeActionApiRequest(`${API_URL}/api/v1/plans/`, "POST", data);
  return result;
}



export async function deletePlanAction(planId: number) {
  const result = await makeActionApiRequest(
    `${API_URL}/api/v1/plans/?plan_id=${planId}`, 
    "DELETE"
  );
  
  if (result.success) {
    revalidatePath("/super-admin/plans");
  }
  
  return result;
}

// Re-export features functions for backward compatibility
export { createPlanFeaturesAction, updatePlanFeaturesAction };

