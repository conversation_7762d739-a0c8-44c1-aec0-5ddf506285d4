"use server";

import { get<PERSON><PERSON>ie } from "@/actions/cookie";
import { I<PERSON>ookies<PERSON>ey } from "@/types";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export async function assignTemplateToUsersAction({ user_ids, template_id }: { user_ids: number[]; template_id: number }) {
  if (!API_URL) return { success: false, error: "API URL not configured" };
  try {
    const token = await getCookie(ICookiesKey.AUTHTOKEN);
    if (!token) return { success: false, error: "Authentication token not found" };

    const body = new URLSearchParams();
    // API expects comma-separated or repeated? We'll send comma-separated for multiple ids
    body.set("user_ids", user_ids.join(","));
    body.set("template_ids", String(template_id));

    const res = await fetch(`${API_URL}/api/v1/templates/assign-template`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
        "ngrok-skip-browser-warning": "1",
      },
      body: body.toString(),
    });

    if (!res.ok) {
      const text = await res.text();
      return { success: false, error: text || `Request failed with ${res.status}` };
    }
    const data = await res.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Failed to assign template" };
  }
}

export async function assignTemplateToSchoolsAction({ school_id, template_ids }: { school_id: number; template_ids: number[] }) {
  if (!API_URL) return { success: false, error: "API URL not configured" };
  try {
    const token = await getCookie(ICookiesKey.AUTHTOKEN);
    if (!token) return { success: false, error: "Authentication token not found" };

    const body = new URLSearchParams();
    body.set("school_ids", String(school_id));
    body.set("template_ids", template_ids.join(","));
    console.log(body.toString());
    const res = await fetch(`${API_URL}/api/v1/templates/assign-template`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
        "ngrok-skip-browser-warning": "1",
      },
      body: body.toString(),
    });

    if (!res.ok) {
      const text = await res.text();
      return { success: false, error: text || `Request failed with ${res.status}` };
    }
    const data = await res.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Failed to assign template to schools" };
  }
}


