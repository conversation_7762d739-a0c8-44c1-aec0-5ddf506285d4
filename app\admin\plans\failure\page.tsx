import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { XCircle } from 'lucide-react'

export default function Page() {


  
  return (
    <div className="min-h-screen flex flex-col bg-muted/10">
      <div className="flex-1 flex items-center justify-center px-4">
        <div className="max-w-lg w-full text-center space-y-4">
          <div className="mx-auto h-20 w-20 rounded-full bg-red-100 flex items-center justify-center">
            <XCircle className="h-10 w-10 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold">Payment failed</h1>
          <p className="text-sm text-gray-600">There was an issue processing your payment. Please try again or contact support.</p>
        </div>
      </div>
      <div className="w-full max-w-md mx-auto px-4 pb-8">
        <Button asChild className="w-full h-10">
          <Link href="/admin/plans">Back to Plans</Link>
        </Button>
      </div>
    </div>
  )
}
