import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import Image from 'next/image'
import { fetchSubscriptionBySessionId, type SubscriptionNormalized } from '@/lib/admin/subscription'
import AutoRedirect from '@/components/common/AutoRedirect'


export default async function SuccessPage({searchParams}: {searchParams: Promise<{ [key: string]: string | string[] | undefined }>}) {
  const params = await searchParams;
  const session_id = params.session_id as string | undefined;
  let sub: SubscriptionNormalized | undefined;
  if (session_id) {
    const res = await fetchSubscriptionBySessionId(session_id);
    if (res.success) sub = res.data;
  }

  return (
    <div className="min-h-screen flex flex-col bg-muted/10">
      <div className="flex-1 flex items-center justify-center px-4">
        <div className="max-w-lg w-full text-center space-y-4">
          <div className="mx-auto h-20 w-20 rounded-full bg-green-100 flex items-center justify-center">
            <Image src="/check.png" alt="Success" width={48} height={48} />
          </div>
          <h1 className="text-2xl font-bold">Plan purchased successfully</h1>
          <p className="text-sm text-gray-600">You can now start using your plan. A receipt has been sent to your email.</p>
          <AutoRedirect to="/admin/dashboard" seconds={5} />
          {sub && (
            <div className="mt-4 text-left bg-white border rounded-lg p-4 space-y-1">
              <div className="flex justify-between text-sm"><span className="text-gray-500">Plan</span><span className="font-medium">{sub.planTitle}</span></div>
              <div className="flex justify-between text-sm"><span className="text-gray-500">Cycle</span><span className="font-medium">{sub.cycle}</span></div>
              <div className="flex justify-between text-sm"><span className="text-gray-500">Amount</span><span className="font-medium">${sub.amount} / {sub.cycle.toLowerCase()}</span></div>
              <div className="flex justify-between text-sm"><span className="text-gray-500">Auto Pay</span><span className="font-medium">{sub.isAutoPay ? 'On' : 'Off'}</span></div>
              <div className="flex justify-between text-sm"><span className="text-gray-500">Starts</span><span className="font-medium">{new Date(sub.startDate).toLocaleDateString()}</span></div>
              <div className="flex justify-between text-sm"><span className="text-gray-500">Ends</span><span className="font-medium">{new Date(sub.endDate).toLocaleDateString()}</span></div>
            </div>
          )}
        </div>
      </div>
      <div className="w-full max-w-md mx-auto px-4 pb-8">
        <Button asChild className="w-full h-10">
          <Link href="/admin/plans">Go to My Plan</Link>
        </Button>
      </div>
    </div>
  )
}
