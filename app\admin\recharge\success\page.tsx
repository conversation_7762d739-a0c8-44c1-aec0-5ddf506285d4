import React from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import Image from 'next/image'
// import { fetchRechargeBySessionId } from '@/lib/admin/subscription'
import AutoRedirect from '@/components/common/AutoRedirect'

export default async function RechargeSuccessPage({ searchParams }: { searchParams: Promise<{ [key: string]: string | string[] | undefined }> }) {
  // const params = await searchParams



  return (
    <div className="min-h-screen w-full flex flex-col justify-center items-center bg-muted/10">
      <div className="flex  items-center justify-center px-4">
        <div className="max-w-lg w-full text-center space-y-4">
          <div className="mx-auto h-20 w-20 rounded-full bg-green-100 flex items-center justify-center">
            <Image src="/check.png" alt="Success" width={48} height={48} />
          </div>
          <h1 className="text-2xl font-bold">Recharge successful</h1>
          <p className="text-sm text-gray-600">Your credits have been added to your account.</p>
        <AutoRedirect to="/admin/dashboard" seconds={5} />
        </div>
      </div>
      <div className="w-full max-w-md py-8 px-4 pb-8">
        <Button asChild className="w-full h-10">
          <Link href="/admin/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    </div>
  )
}


