import { NextRequest, NextResponse } from "next/server";
import { fetchActiveSubscription } from "@/lib/admin/subscription";

export async function GET(_request: NextRequest) {
  try {
    const res = await fetchActiveSubscription();
    
    return NextResponse.json(res, { status: res.success ? 200 : 401 });
  } catch (error) {
    return NextResponse.json({ success: false, error: String(error) }, { status: 500 });
  }
}


