@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Sansation';
  src: url('/fonts/Sansation/Sansation-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Sansation';
  src: url('/fonts/Sansation/Sansation-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

.sansation-font {
  font-family: 'Sansation', Arial, sans-serif;
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
@layer base {
  :root {
    /* Backgrounds */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Primary */
    --primary: 220 60% 45%;
    --primary-foreground: 0 0% 100%;

    /* Secondary / Neutrals */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    /* Error */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Borders and Forms */
    --border: 240 85% 79%;
    --input: 0 0% 89.8%;
    --ring: 240 85% 79%;

    /* Charts (Light Theme) */
    --chart-1: 220 70% 50%;  /* Blue */
    --chart-2: 160 60% 45%;  /* Green */
    --chart-3: 30 80% 55%;   /* Orange */
    --chart-4: 280 65% 60%;  /* Purple */
    --chart-5: 340 75% 55%;  /* Pink/Red */

    /* Sidebar Colors (Light) */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --radius: 0.5rem;
  }

  .dark {
    /* Backgrounds */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary */
    --primary: 220 60% 50%;
    --primary-foreground: 0 0% 100%;

    /* Secondary / Neutrals */
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    /* Error */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Borders and Forms */
    --border: #8D8EF5;
    --input: 0 0% 14.9%;
    --ring: 220 60% 70%;

    /* Charts (Dark Theme) */
    --chart-1: 220 70% 60%;
    --chart-2: 160 60% 55%;
    --chart-3: 30 80% 65%;
    --chart-4: 280 65% 70%;
    --chart-5: 340 75% 65%;

    /* Sidebar Colors (Dark) */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
