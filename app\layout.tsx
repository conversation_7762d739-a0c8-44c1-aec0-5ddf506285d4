
import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/components/auth/auth-provider'
import { ToastProvider } from '@/components/hooks/use-toast'
import TopLoader from 'nextjs-toploader';

export const metadata: Metadata = {
  title: 'Notifly',
  description: 'Created with v0',
  generator: 'v0.dev',
   icons: {
    icon: '/logo-icon.png', 
  }
}


export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
       <head>
       <link href="https://fonts.googleapis.com/css2?family=Sansation:wght@400;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.google.com/share?selection.family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900" rel="stylesheet" />
      </head>
      <body className="font-inter">
        <TopLoader showSpinner={false}/>
        <AuthProvider>
          <ToastProvider />
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}


