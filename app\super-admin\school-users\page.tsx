"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { AdminLayout } from "@/components/layout/admin-layout"
import { ArrowLeft, Search, Loader2, User2, Mail, Phone, Calendar, Shield } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { isTokenExpired, refreshAccessToken } from "@/lib/auth"
import SearchFilter from "@/components/super-admin/schools/SearchFilter"

interface User {
  id: number
  name: string
  email: string
  phone: string
  role: string
  status: "active" | "inactive"
  joinDate: string
}

function SchoolUsersContent() {
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [schoolName, setSchoolName] = useState("")
  const [adminUser, setAdminUser] = useState<User | null>(null)
  const schoolId = searchParams.get('schoolId')
  const searchTerm = searchParams.get("search") || "";

  useEffect(() => {
    if (!schoolId) {
      window.location.href = "/super-admin/dashboard"
      return
    }

    setSchoolName(searchParams.get('schoolName') || "School")
    fetchUsers()
  }, [schoolId])

  // Filter users based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users)
      return
    }
    
    const lowercaseSearch = searchTerm.toLowerCase()
    const filtered = users.filter(user => 
      user.name.toLowerCase().includes(lowercaseSearch) ||
      user.email.toLowerCase().includes(lowercaseSearch) ||
      user.role.toLowerCase().includes(lowercaseSearch)
    )
    
    setFilteredUsers(filtered)
  }, [searchTerm, users])

  const fetchUsers = async () => {
    setIsLoading(true)
    try {
      // Check if token is expired and refresh if needed
      if (isTokenExpired('super-admin')) {
        const refreshed = await refreshAccessToken('super-admin')
        if (!refreshed) {
          toast({
            variant: "destructive",
            title: "Session Expired",
            description: "Please login again to continue.",
          })
          setTimeout(() => {
            window.location.href = "/super-admin/login"
          }, 1500)
          return
        }
      }
      
      const token = localStorage.getItem("superAdminAccessToken")
      
      if (!token) {
        toast({
          variant: "destructive",
          title: "Authentication Error",
          description: "You are not logged in. Please login to continue.",
        })
        setTimeout(() => {
          window.location.href = "/super-admin/login"
        }, 1500)
        return
      }
      
      const API_URL = process.env.NEXT_PUBLIC_API_URL
      
      // Use the specific API endpoint for getting users by school ID
      const response = await fetch(
        `${API_URL}/api/v1/auth/get-specific-user?school_id=${schoolId}&limit=20&offset=0`,
        {
          method: 'GET',
          headers: {
            'accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': '1'
          }
        }
      )
      
      if (!response.ok) {
        throw new Error(`API responded with status ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.status !== 200) {
        throw new Error(data.message || "Failed to fetch users")
      }
      
      // Transform API data to match our User type
      const transformedUsers: User[] = data.data.result.map((item: any) => ({
        id: item.id,
        name: item.name || item.email.split('@')[0],
        email: item.email,
        phone: item.mobile_no || "N/A",
        role: item.role || "User",
        status: item.is_active ? "active" : "inactive",
        joinDate: item.created_at // keep as ISO string for sorting/formatting
      }))

      // Separate admin and teachers
      const admin = transformedUsers.find(u => u.role.toLowerCase() === "admin") || null
      const teachers = transformedUsers.filter(u => u.role.toLowerCase() === "teacher")
        .sort((a, b) => new Date(b.joinDate).getTime() - new Date(a.joinDate).getTime())

      setAdminUser(admin)
      setUsers(teachers)
      setFilteredUsers(teachers)
      
    } catch (error: any) {
      console.error("Error fetching users:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to load users",
        variant: "destructive"
      })
      setUsers([])
      setFilteredUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToSchools = () => {
    window.location.href = "/super-admin/schools"
  }
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
  return (
    <AdminLayout type="super-admin">
      <div className="space-y-6 max-w-6xl mx-auto px-2 md:px-0">
      

        {/* Admin Profile Card */}
        {isLoading ? (
          <div className="relative bg-gradient-to-tr from-blue-50/80 to-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 flex flex-col md:flex-row items-center gap-8 border border-blue-100 overflow-hidden animate-pulse">
            <div className="flex-shrink-0">
              <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center" />
            </div>
            <div className="flex-1 min-w-0 w-full">
              <div className="flex items-center gap-2 mb-3">
                <span className="h-6 w-40 bg-gray-200 rounded" />
                <span className="h-5 w-14 bg-blue-100 rounded-full ml-2" />
              </div>
              <div className="flex flex-wrap gap-3 mt-2">
                <span className="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 text-blue-900 text-sm font-medium shadow-sm">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-200" />
                  <span className="inline-block h-4 w-28 bg-gray-200 rounded" />
                </span>
                <span className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-50 text-green-900 text-sm font-medium shadow-sm">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-green-200" />
                  <span className="inline-block h-4 w-20 bg-gray-200 rounded" />
                </span>
                <span className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-50 text-gray-700 text-xs font-medium shadow-sm">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-gray-200" />
                  <span className="inline-block h-4 w-24 bg-gray-200 rounded" />
                </span>
              </div>
            </div>
            <div className="absolute right-0 top-0 w-32 h-32 bg-blue-100/30 rounded-full blur-2xl opacity-60 pointer-events-none" />
          </div>
        ) : (
          <div className="relative bg-gradient-to-tr from-blue-50/80 to-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 flex flex-col md:flex-row items-center gap-8 border border-blue-100 overflow-hidden">
            <div className="flex-shrink-0">
              <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-200 to-blue-400 flex items-center justify-center text-5xl font-bold text-white shadow-lg">
                {adminUser ? adminUser.name.charAt(0).toUpperCase() : <User2 className="text-blue-300 w-12 h-12" />}
              </div>
            </div>
            <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
            <div className="">
                <span className="text-2xl font-bold text-gray-900 truncate">{adminUser ? adminUser.name : "No Admin Assigned"}</span>
                <span className="inline-flex items-center  gap-1  bg-blue-100 text-blue-700 px-3 py-1  rounded-full text-xs font-semibold "><Shield className="w-4 h-4" /> Admin</span>
              </div>
              <SearchFilter  placeholder="Enter Teacher Name, Email...."/>
              </div>
              {/* Enhanced Admin Contact Info */}
              <div className="flex flex-wrap gap-3 mt-2">
                <a
                  href={adminUser ? `mailto:${adminUser.email}` : undefined}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 hover:bg-blue-100 transition text-blue-900 text-sm font-medium shadow-sm"
                  target="_blank"
                  rel="noopener noreferrer"
                  tabIndex={adminUser ? 0 : -1}
                >
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-200">
                    <Mail className="w-4 h-4 text-blue-600" />
                  </span>
                  {adminUser && adminUser.email ? (
                    <span className="truncate max-w-[140px]">{adminUser.email}</span>
                  ) : (
                    <span className="inline-block h-4 w-28 bg-gray-200 rounded animate-pulse" />
                  )}
                </a>
                <div
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-50 hover:bg-green-100 transition text-green-900 text-sm font-medium shadow-sm"
                >
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-green-200">
                    <Phone className="w-4 h-4 text-green-600" />
                  </span>
                  {adminUser && adminUser.phone && adminUser.phone !== "N/A" ? (
                    <span className="truncate max-w-[110px]">{adminUser.phone}</span>
                  ) : (
                    <span className="inline-block h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  )}
                </div>
                {adminUser && (
                  <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-50 text-gray-700 text-xs font-medium shadow-sm">
                    <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-gray-200">
                      <Calendar className="w-4 h-4 text-gray-500" />
                    </span>
                    <span>Joined {formatDate(adminUser.joinDate)}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="absolute right-0 top-0 w-32 h-32 bg-blue-100/30 rounded-full blur-2xl opacity-60 pointer-events-none" />
          </div>
        )}

        {/* Teachers Table */}
        {isLoading ? (
          <div className="bg-white/80 backdrop-blur rounded-2xl shadow-xl p-6 border border-gray-100 animate-pulse">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 rounded-full bg-blue-100" />
                <span className="h-6 w-32 bg-gray-200 rounded" />
              </div>
              <span className="h-5 w-16 bg-gray-100 rounded" />
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-blue-50/60">
                    <th className="py-3 px-4"><span className="h-4 w-16 bg-gray-200 rounded block" /></th>
                    <th className="py-3 px-4"><span className="h-4 w-20 bg-gray-200 rounded block" /></th>
                    <th className="py-3 px-4"><span className="h-4 w-16 bg-gray-200 rounded block" /></th>
                    <th className="py-3 px-4"><span className="h-4 w-14 bg-gray-200 rounded block" /></th>
                    <th className="py-3 px-4"><span className="h-4 w-20 bg-gray-200 rounded block" /></th>
                  </tr>
                </thead>
                <tbody>
                  {[...Array(5)].map((_, i) => (
                    <tr key={i} className={i % 2 === 0 ? "bg-white/70" : "bg-blue-50/40"}>
                      <td className="py-3 px-4"><span className="h-4 w-24 bg-gray-200 rounded block" /></td>
                      <td className="py-3 px-4"><span className="h-4 w-32 bg-gray-200 rounded block" /></td>
                      <td className="py-3 px-4"><span className="h-4 w-20 bg-gray-200 rounded block" /></td>
                      <td className="py-3 px-4"><span className="h-4 w-16 bg-gray-200 rounded block" /></td>
                      <td className="py-3 px-4"><span className="h-4 w-24 bg-gray-200 rounded block" /></td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur rounded-2xl shadow-xl p-6 border border-gray-100">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User2 className="w-6 h-6 text-blue-400" />
                <h3 className="text-xl font-semibold text-gray-900">Teachers</h3>
              </div>
              <span className="text-sm text-gray-500">{filteredUsers.length} teacher{filteredUsers.length !== 1 ? 's' : ''}</span>
            </div>
            <div className="overflow-x-auto border  rounded-xl">
              <Table className="min-w-full rounded-xl ">
                <TableHeader>
                  <TableRow className="bg-blue-50/60">
                    <TableHead className="py-3 px-4 text-gray-700 font-semibold">Name</TableHead>
                    <TableHead className="py-3 px-4 text-gray-700 font-semibold">Email</TableHead>
                    <TableHead className="py-3 px-4 text-gray-700 font-semibold">Phone</TableHead>
                    <TableHead className="py-3 px-4 text-gray-700 font-semibold">Status</TableHead>
                    {/* <TableHead className="py-3 px-4 text-gray-700 font-semibold">Role</TableHead> */}
                    <TableHead className="py-3 px-4 text-gray-700 font-semibold">Join Date</TableHead>

                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-5 text-gray-400 text-sm">
                        {users.length === 0 ? "No teachers found" : "No matching teachers found"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user, idx) => (
                      <TableRow key={user.id} className={idx % 2 === 0 ? "bg-white/70" : "bg-blue-50/40 hover:bg-blue-100/40 transition"}>
                        <TableCell className="py-3 px-4 font-medium text-gray-900">{user.name}</TableCell>
                        <TableCell className="py-3 px-4 text-gray-700">{user.email}</TableCell>
                        <TableCell className="py-3 px-4 text-gray-700">{user.phone}</TableCell>
                        
                        <TableCell className="py-3 px-4">
                          <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-semibold ${
                            user.status === 'active' 
                              ? 'bg-green-100 text-green-700' 
                              : 'bg-red-100 text-red-700'
                          }`}>
                            {user.status === 'active' ? <span className="w-2 h-2 rounded-full bg-green-500 inline-block" /> : <span className="w-2 h-2 rounded-full bg-red-500 inline-block" />} {user.status}
                          </span>
                        </TableCell>
                        {/* <TableCell className="py-3 px-4 text-gray-700">{user.role}</TableCell> */}
                        <TableCell className="py-3 px-4 text-gray-700">{user.joinDate ? formatDate(user.joinDate) : "N/A"}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="flex justify-center items-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  )
}

// Main page component with Suspense boundary
export default function SchoolUsersPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SchoolUsersContent />
    </Suspense>
  )
}

