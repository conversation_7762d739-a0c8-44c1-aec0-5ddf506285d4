import TablePagination from '@/components/common/TablePagination'
import SchoolHeader from '@/components/super-admin/schools/SchoolHeader'
import SchoolTable from '@/components/super-admin/schools/table/SchoolTable'
import { getSchoolsBySuperadmin } from '@/lib/superadmin/school'
type SearchParams = Promise<{ [search: string]: string | undefined }>

export default async function page({ searchParams }: { searchParams: SearchParams }) {
  const resolvedSearchParams = await searchParams;
  const searchText=resolvedSearchParams.search;
  const limit = parseInt(resolvedSearchParams.limit ?? "5");
  const offset = parseInt(resolvedSearchParams.offset ?? "0");
  const newoffset = (limit - 1) * offset;

  const data = await getSchoolsBySuperadmin({
    searchText: searchText ? searchText : "",
    limit,offset:newoffset
    
  })
  return (
    // <AdminLayout type={IRole.SUPER_ADMIN}>

      <>
      <SchoolHeader />
      <SchoolTable data={data?.data?.result} />
      <TablePagination total={data.data.total_count} /></>
    // </AdminLayout>
  )
}
