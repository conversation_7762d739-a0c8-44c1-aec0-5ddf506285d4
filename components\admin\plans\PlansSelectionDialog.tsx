"use client";

import { useEffect, useState } from "react";
import PlansGrid from "./PlansGrid";
import { ICookiesKey, PlanResponse } from "@/types";
import { Dialog, DialogContent } from "@/components/ui/dialog";

export default function BillingCycleToggle({
  dialogMode,
}: {
  dialogMode: boolean;
}) {
  const [isMonthly, setIsMonthly] = useState(true);
  const [plans, setPlans] = useState<PlanResponse[]>([]);
  const [open, setOpen] = useState(dialogMode);
  const [currentPlanId, setCurrentPlanId] = useState<number | undefined>(
    undefined
  );
  useEffect(() => {
    const fetchPlans = async () => {
      const response = await fetch(`/api/plans?is_monthly=${isMonthly}`);
      const data = await response.json();
      setPlans(data.data.plans);
    };
    fetchPlans();
  }, []);

  useEffect(() => {
    const fetchSubscription = async () => {
      const response = await fetch(`/api/subscription`);
      const data = await response.json();
      console.log("data subscription", data);
      localStorage.setItem(
        ICookiesKey.SUBSCRIPTION,
        JSON.stringify(data.data.result)
      );
      setCurrentPlanId(data.data.planId);
      if (data.data?.price == 0) {
        setOpen(true);
      }
    };

    if (dialogMode) {
      fetchSubscription();
    }
  }, []);

  function PlansSelection({
    onChosen,
    currentPlanId,
  }: {
    onChosen: () => void;
    currentPlanId?: number;
  }) {
    return (
      <div className="flex flex-col h-full gap-4">
        <div className="h-12 flex justify-center">
          <div className="bg-gray-100 rounded-full p-1 inline-flex border border-gray-200">
            <button
              onClick={() => setIsMonthly(true)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                isMonthly
                  ? "bg-white text-indigo-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsMonthly(false)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                !isMonthly
                  ? "bg-white text-indigo-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Yearly
            </button>
          </div>
        </div>
        <PlansGrid
          plans={plans}
          is_monthly={isMonthly}
          currentPlanId={currentPlanId}
          onChosen={onChosen}
        />
      </div>
    );
  }

  function handleChosen() {
    setOpen(false);
  }

  return dialogMode ? (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        hideClose={dialogMode}
        onPointerDownOutside={(e) => {
          if (dialogMode) e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          if (dialogMode) e.preventDefault();
        }}
        className="max-w-5xl w-[95vw] min-h-[70vh] overflow-y-auto"
      >
        <PlansSelection onChosen={handleChosen} currentPlanId={currentPlanId} />
      </DialogContent>
    </Dialog>
  ) : (
    <PlansSelection onChosen={handleChosen} currentPlanId={currentPlanId} />
  );
}
