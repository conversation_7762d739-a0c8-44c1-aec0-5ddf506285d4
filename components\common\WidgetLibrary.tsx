
import EditSchool from "../super-admin/schools/widgets/EditSchool";
import GoToSchoolLink from "../super-admin/schools/widgets/GoToSchoolLink";
import SchoolActions from "../super-admin/schools/widgets/SchoolActions";
import SchoolContactWidget from "../super-admin/schools/widgets/SchoolContactWidget";
import SchoolStatusToggle from "../super-admin/schools/widgets/SchoolStatusToggle";
import StatusWidget from "./StatusWidget";


const widgets: any = {
  statusWidget: () => {
    return <StatusWidget />;
  },
  // memberDetailWidget: (value: string, rowData: IMember) => {
  //   console.log("value", value);
  //   return <MemberDetailWidget rowData={rowData} />;
  // },

  // memberTableStatus: (value: string, rowData: IMember) => {
  //   console.log("rowData", rowData);
  //   return <MemberTableStatus value={value} />;
  // },

  // editAndDeleteMemberWidget: () => {
  //   return <EditAndDeleteMemberWidget />;
  // },
  // classDetailWidget: (value: string, rowData: IClass) => {
  //   console.log("value", value);
  //   return <ClassDetailWidget rowData={rowData} />;
  // },

  // classTableStatus: (value: string, rowData: IMember) => {
  //   console.log("rowData", rowData);
  //   return <ClassTableStatus value={value} />;
  // },
  // editAndDeleteClassWidget: () => {
  //   return <EditAndDeleteClassWidget />;
  // },
  // classCapacityWidget: (value: string, rowData: IClass) => {
  //   console.log("value", value);
  //   return <ClassCapacityWidget rowData={rowData} />;
  // },
  // equipmentDetailWidget: (value: string, rowData: IEquipment) => {
  //   console.log("value", value);
  //   return <EquipmentDetailWidget rowData={rowData} />;
  // },

  // ownerStatusBadge: (value: string) => {
  //   return <OwnerStatusBadge value={value} />;
  // },
  schoolActions: (value: string,rowData:any) => {
    return <SchoolActions  schoolData={rowData} />;
  },
  schoolStatusToggle: (value: string,rowData:any) => {
    return <SchoolStatusToggle  schoolData={rowData} />;
  },
  schoolContactWidget: (value: string,rowData:any) => {
    return <SchoolContactWidget schoolData={rowData} />;
  },
  
};
export default function WidgetLibrary({
  widgetName,
  value,
  rowData,
}: {
  widgetName: string;
  value: string;
  rowData?: any;
  className?: string;
}) {
  return widgets[widgetName](value, rowData);
}
