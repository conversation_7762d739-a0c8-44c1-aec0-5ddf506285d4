import SortableHeaderCell from "./SortableHeaderCell";
import { ITableMetadata } from "../../common/table/Table";
import { Label } from "../../common/Label";
import { cn } from "@/lib/utils";

export default function TableHeader({
    metadata,
    className,
}: {
    metadata: ITableMetadata[];
    className?: string;
}) {
    return (
        <div
            className={cn(
                " md:flex  md:items-center hidden md:py-2 bg-gray-200  border-box ",
                className,
            )}
        >
            {metadata.map((meta, index) =>
                meta.sortable ? (
                    <SortableHeaderCell
                        key={index}
                        columnName={meta.columnName}
                        label={meta.headerLabel || ""}
                        defaultSortColumn={meta.defaultSortColumn}
                        defaultSortOrder={meta.defaultSortOrder}
                        className={cn("hidden lg:table-cell", meta.columnClass)}
                        iconClass={meta.sortableIconClass}
                    />
                ) : (
                    <Label
                        key={index}
                        variant={"semibold"}
                        size={"sm"}
                        className={cn(
                            "font-semibold text-sm  ",
                            meta.columnClass,
                        )}
                    >
                        {meta.headerLabel}
                    </Label>
                ),
            )}
        </div>
    );
}
