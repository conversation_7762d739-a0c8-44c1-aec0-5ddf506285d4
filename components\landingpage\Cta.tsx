import { StoreButtons } from "./StoreButton";


export default function Cta() {
  
  return (
    <section className="max-w-[90rem] px-4 sm:px-8 md:px-16 mx-auto py-10 md:py-16">
      <div className=" px-0 md:px-6 mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start ">
          <div className="space-y-6">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl  text-gray-900  font-medium ">
            Empowering Educators
              <br />
              Connecting Staff
              <br/>
              Engaging Families
            </h2>
            <div className="space-y-4">
              <p className="text-gray-600 text-sm md:text-base">
              Notifly is the fastest way to keep your communication streamlined.
              </p>
              <p className="text-gray-600 text-sm md:text-base sm:w-[75%]">
              Designed for speed and built for real-world workflows, Notifly helps educators send, organize, and manage messages effortlessly. Send your update where it needs to go — instantly and reliably.
              </p>
              <div>
              <button className="cursor-pointer bg-gradient-to-r from-[#77A1D3] via-[#79CBCA] to-[#77A1D3] bg-[length:200%_auto] hover:bg-[position:100%] text-white px-8 py-3 m-2.5 rounded-full shadow-[0_0_20px_#eee] transition-all duration-500 text-sm md:text-base">
              Parent Sign-Up
              </button>
            </div>
            </div>
         <StoreButtons/>
          </div>
          <div className="grid grid-cols-2 gap-4 mt-8 sm:mt-0">
            <div className="aspect-[5/6] rounded-lg overflow-hidden bg-purple-50">
              <img
                src={"girlwithbook.jpg"}
                alt="Student with bow tie"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="aspect-[5/6] rounded-lg overflow-hidden bg-green-50">
              <img
                src={"studentteacher.png"}
                alt="Student with curly hair"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="aspect-[5/6] rounded-lg overflow-hidden bg-blue-50 col-span-2 sm:col-span-1">
              <img
                src={"girlboy.png"}
                alt="Student with books"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="aspect-[5/6] rounded-lg overflow-hidden bg-purple-50 col-span-2 sm:col-span-1">
              <img
                src={"boywithphone.png"}
                alt="Student with notebook"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
