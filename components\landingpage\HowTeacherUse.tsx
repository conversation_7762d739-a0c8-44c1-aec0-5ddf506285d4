'use client'
import { useState } from "react";
import video from "../../public/video.png";

// Play icon component
const PlayIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="48"
    viewBox="0 0 24 24"
    fill="white"
    stroke="white"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="opacity-80"
  >
    <polygon points="5 3 19 12 5 21 5 3"></polygon>
  </svg>
);

// Download icon component
const DownloadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
    <polyline points="7 10 12 15 17 10"></polyline>
    <line x1="12" y1="15" x2="12" y2="3"></line>
  </svg>
);

const HowTeacherUse = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayVideo = () => {
    setIsPlaying(true);
    // In a real implementation, you would play the video here
  };

  return (
    <section className="w-full py-16 bg-white">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Video Section */}
          <div className="relative rounded-lg overflow-hidden shadow-lg">
            {isPlaying ? (
              <video
                controls
                autoPlay
                className="w-full aspect-[4/3] object-cover rounded-lg"
                src="https://example.com/your-video.mp4" // Replace with your actual video URL
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <div
                className="relative cursor-pointer"
                onClick={handlePlayVideo}
              >
                <img
                  src={video.src}
                  alt="Teachers using Notifly in classroom"
                  className="w-full aspect-[4/3] object-cover rounded-lg"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="rounded-full bg-black bg-opacity-50 p-4 flex items-center justify-center">
                    <PlayIcon />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="flex flex-col space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
              How Teachers Use
              <br />
              Notifly: Watch Now
            </h2>
            <p className="text-gray-600">
              Lorem Ipsum Dolor Sit Amet, Consectetur Adipiscing Elit, Sed Do
              Eiusmod Tempor Incididunt Ut Labore Et Dolore Magna Aliqua. Ut
              Enim Ad Minim Veniam, Quis Nostrud Exercitation Ullamco Laboris
              Nisi Ut Aliquip Ex Ea Commodo Consequat. Duis Aute Irure Dolor In
              Reprehenderit In Voluptate Velit Esse Cillum Dolore Eu Fugiat
              Nulla Pariatur.
            </p>
            <div className="flex flex-wrap gap-4">
              <button className="px-6 py-3 rounded-full bg-gradient-to-r from-teal-500 to-purple-600 text-white font-medium flex items-center gap-2 hover:opacity-90 transition-opacity">
                <DownloadIcon />
                Download Application
              </button>
              <button className="px-6 py-3 rounded-full border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 transition-colors">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowTeacherUse;
