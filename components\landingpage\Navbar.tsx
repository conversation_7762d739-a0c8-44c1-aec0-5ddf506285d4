import OptInForm from "./OptInForm";
import Link from "next/link";
import MobileMenu from "./MobileMenu";

const Navbar = () => {
  return (
    <nav className="w-full max-w-[90rem] mx-auto md:px-16 py-4  flex items-center justify-between">
      {/* Logo */}
      <div className="w-full flex items-center  justify-between px-3 sm:px-0 ">
      <Link href={"/"} >  <img src={"Logo.png"} alt="Notifly Logo" className="  h-11" /></Link>
        <MobileMenu />
      </div>
      <div className="w-full hidden md:flex flex-row gap-10 items-center ml-auto ">
        <ul className="self-end flex flex-row gap-10 items-center ml-auto">
          {/* <li>Home</li> */}
          {/* <li>How It Works</li> */}
          <OptInForm/>
          
          <Link href="/admin/signup">
          <button className="cursor-pointer bg-gradient-to-r from-[#77A1D3] via-[#79CBCA] to-[#77A1D3] bg-[length:200%_auto] hover:bg-[position:100%] text-white px-8 py-3 m-2.5 rounded-full shadow-[0_0_20px_#eee] transition-all duration-500 text-sm md:text-base">
           Teacher Sign-Up
          </button>
          </Link>
        </ul>
      </div>
    </nav>
  );
};

export default Navbar;
