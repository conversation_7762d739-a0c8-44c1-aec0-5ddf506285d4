"use client";

import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import CommonButton from "@/components/common/Button";
import { assignTemplateToUsersAction } from "@/actions/super-admin/template";
import { fetchTemplates, type Template } from "@/lib/superadmin/template";
import { Checkbox } from "@/components/ui/checkbox";
import { paginationLimit } from "@/types";
import { successToast } from "@/components/hooks/use-toast";

export default function AssignTemplatesToTeacherDialog({ teacherId, children }: { teacherId: number; children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [filter, setFilter] = useState("");

  useEffect(() => {
    if (!open) return;
    setError(null);
    (async () => {
      const res = await fetchTemplates(0, { limit: paginationLimit.LIMIT_50 } as any);
      setTemplates(res.templates || []);
    })();
  }, [open]);

  const filtered = useMemo(() => {
    const q = filter.trim().toLowerCase();
    if (!q) return templates;
    return templates.filter(t => (t.name || "").toLowerCase().includes(q) || (t.subject || "").toLowerCase().includes(q));
  }, [filter, templates]);

  const toggleAll = (checked: boolean) => {
    if (checked) setSelected(new Set(filtered.map(t => t.id)));
    else setSelected(new Set());
  };

  const toggleOne = (id: number, checked: boolean) => {
    setSelected(prev => {
      const next = new Set(prev);
      if (checked) next.add(id); else next.delete(id);
      return next;
    });
  };

  const assign = async () => {
    if (selected.size === 0) return;
    setLoading(true);
    setError(null);
    // API expects user_ids multiple and a single template id, but now requirement is single teacher, multiple templates.
    // We'll loop templates and call once per template to match the existing endpoint contract.
    const templateIds = Array.from(selected);
    for (const templateId of templateIds) {
      const res = await assignTemplateToUsersAction({ user_ids: [teacherId], template_id: templateId });
      if (!res.success) {
        setError((res as any)?.error || "Failed to assign some templates");
        setLoading(false);
        return;
      }
    }
    setLoading(false);
    setOpen(false);
    successToast("Templates assigned successfully");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-2xl w-[95vw] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Assign templates to teacher</DialogTitle>
        </DialogHeader>
        <div className="flex items-center gap-2">
          <input
            placeholder="Search template by name or subject"
            className="w-full border rounded-md px-3 py-2 text-sm"
            value={filter}
            onChange={e => setFilter(e.target.value)}
          />
        </div>
        {error && <div className="text-sm text-red-600">{error}</div>}
        <div className="flex items-center gap-2 text-sm mt-2">
          <Checkbox id="selectAll" checked={selected.size > 0 && selected.size === filtered.length} onCheckedChange={val => toggleAll(Boolean(val))} />
          <label htmlFor="selectAll">Select all ({filtered.length})</label>
        </div>
        <div className="mt-2 flex-1 overflow-y-auto border rounded-md">
          <table className="w-full text-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-3 py-2 w-10"></th>
                <th className="px-3 py-2 text-left">Name</th>
                <th className="px-3 py-2 text-left">Type</th>
                <th className="px-3 py-2 text-left">Subject</th>
              </tr>
            </thead>
            <tbody>
              {filtered.map(t => (
                <tr key={t.id} className="border-t">
                  <td className="px-3 py-2">
                    <Checkbox checked={selected.has(t.id)} onCheckedChange={val => toggleOne(t.id, Boolean(val))} />
                  </td>
                  <td className="px-3 py-2">{t.name}</td>
                  <td className="px-3 py-2 capitalize">{t.type}</td>
                  <td className="px-3 py-2">{t.subject}</td>
                </tr>
              ))}
              {filtered.length === 0 && (
                <tr>
                  <td colSpan={4} className="px-3 py-6 text-center text-gray-500">No templates found</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <div className="flex justify-end gap-2">
          <CommonButton variant="secondary" onClick={() => setOpen(false)} disabled={loading} className="bg-[#8D8EF5] text-white px-4  h-9 rounded-lg hover:bg-[#8D8EF5]/90">Cancel</CommonButton>
          <CommonButton onClick={assign} loading={loading} disabled={selected.size === 0} className="bg-[#8D8EF5] text-white px-4 h-9 rounded-lg hover:bg-[#8D8EF5]/90">Assign</CommonButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}


