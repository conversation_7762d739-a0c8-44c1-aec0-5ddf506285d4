"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Trash2, AlertTriangle } from "lucide-react";
import { deleteUserAction } from "@/actions/super-admin/teacher";
import { errorToast, successToast } from "@/components/hooks/use-toast";

interface DeleteUserDialogProps {
  userId: number;
  userName: string;
  userEmail: string;
}

export default function DeleteUserDialog({
  userId,
  userName,
  userEmail,
}: DeleteUserDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    try {
      setLoading(true);
      const result = await deleteUserAction(userId);

      if (result.status) {
        successToast(`User "${userName}" has been deleted successfully`);
        setOpen(false);
      } else {
        errorToast(result.error || "Failed to delete user");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      errorToast("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
        >
          <Trash2 className="h-4 w-4 text-red-500" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete User
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            This action cannot be undone. This will permanently delete the user
            account.
          </DialogDescription>
        </DialogHeader>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="space-y-2">
            <div className="font-medium text-red-800">User Details:</div>
            <div className="text-sm text-red-700">
              <div>
                <strong>Name:</strong> {userName}
              </div>
              <div>
                <strong>Email:</strong> {userEmail}
              </div>
              <div>
                <strong>ID:</strong> #{userId}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? "Deleting..." : "Delete User"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
