"use client";

import { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import CommonButton from "@/components/common/Button";
import { assignTemplateToSchoolsAction } from "@/actions/super-admin/template";
import { fetchTemplates, type Template } from "@/lib/superadmin/template";
import { Checkbox } from "@/components/ui/checkbox";
import { paginationLimit } from "@/types";
import { successToast } from "@/components/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

export default function AssignTemplatesToSchoolDialog({
  schoolId,
  children,
}: {
  schoolId: number;
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [filter, setFilter] = useState("");

  useEffect(() => {
    if (!open) return;
    setError(null);
    (async () => {
      const res = await fetchTemplates(0, {
        limit: paginationLimit.LIMIT_50,
      } as any);
      setTemplates(res.templates || []);
    })();
  }, [open]);

  const filtered = useMemo(() => {
    const q = filter.trim().toLowerCase();
    if (!q) return templates;
    return templates.filter(
      (t) =>
        (t.name || "").toLowerCase().includes(q) ||
        (t.subject || "").toLowerCase().includes(q)
    );
  }, [filter, templates]);

  const toggleAll = (checked: boolean) => {
    if (checked) setSelected(new Set(filtered.map((t) => t.id)));
    else setSelected(new Set());
  };

  const toggleOne = (id: number, checked: boolean) => {
    setSelected((prev) => {
      const next = new Set(prev);
      if (checked) next.add(id);
      else next.delete(id);
      return next;
    });
  };

  const assign = async () => {
    if (selected.size === 0) return;
    setLoading(true);
    setError(null);

    const templateIds = Array.from(selected);
    const res = await assignTemplateToSchoolsAction({
      school_id: schoolId,
      template_ids: templateIds,
    });

    if (!res.success) {
      setError((res as any)?.error || "Failed to assign templates");
      setLoading(false);
      return;
    }

    setLoading(false);
    setOpen(false);
    successToast("Templates assigned successfully");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Templates to School</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search templates..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="select-all"
              checked={filtered.length > 0 && selected.size === filtered.length}
              onCheckedChange={toggleAll}
            />
            <label htmlFor="select-all" className="text-sm font-medium">
              Select All ({filtered.length})
            </label>
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filtered.map((template) => (
              <div
                key={template.id}
                className="flex items-center space-x-2 p-2 border rounded"
              >
                <Checkbox
                  id={`template-${template.id}`}
                  checked={selected.has(template.id)}
                  onCheckedChange={(checked) =>
                    toggleOne(template.id, checked as boolean)
                  }
                />
                <label
                  htmlFor={`template-${template.id}`}
                  className="flex-1 cursor-pointer"
                >
                  <div className="font-medium">{template.name}</div>
                  <div className="text-sm text-gray-500">
                    {template.subject}
                  </div>
                </label>
              </div>
            ))}
          </div>

          {error && <div className="text-red-500 text-sm">{error}</div>}

          <div className="flex justify-end space-x-2">
            <CommonButton
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </CommonButton>
            <CommonButton
              onClick={assign}
              disabled={selected.size === 0 || loading}
              loading={loading}
            >
              Assign {selected.size} Template{selected.size !== 1 ? "s" : ""}
            </CommonButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
