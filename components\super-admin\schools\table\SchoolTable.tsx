import { Label } from "@/components/common/Label";
import { ITableMetadata } from "@/components/common/table/Table";
import TableHeader from "@/components/common/table/TableHeader";
import TableRow from "@/components/common/table/TableRow";
import { cn } from "@/lib/utils";

interface SchoolTableProps {
    data: [];
    className?: string;
}

const SchoolMetadata: ITableMetadata[] = [
  {
    columnName: "name",
    headerLabel: "Name",
    sortable: false,
    columnClass:
      "w-full md:w-[20%] text-left md:bg-gray-200 md:py-2  md:pl-4 ",
    cellClass: "w-full md:w-[20%] text-left md:pl-1.5 truncate pr-4",
    cellContainerClass: "truncate",
  },
  {
    columnName: "address",
    headerLabel: "Address",
    sortable: false,
    columnClass:
      "w-full md:w-[24%] text-left md:bg-gray-200 md:py-2",
    cellClass: "w-full md:w-[24%]",
    cellContainerClass: "truncate",
  },
  {
    columnName: "city",
    headerLabel: "City",
    sortable: false,
    columnClass:
      "w-full md:w-[12%] text-left md:bg-gray-200 md:py-2",
    cellClass: "w-full md:w-[12%]",
  },
  {
    columnName: "is_active",
    headerLabel: "Status",
    sortable: false,
    columnClass:
      "w-full md:w-[12%] text-left md:bg-gray-200 md:py-2",
    cellClass: "w-full md:w-[12%] md:px-0",
    type: "widget",
    widgetName: "schoolStatusToggle",
  },
  {
    columnName: "",
    headerLabel: "Contact",
    sortable: false,
    columnClass:
      "w-full md:w-[18%] text-left md:bg-gray-200 md:py-2",
    cellClass: "w-full md:w-[18%]",
    cellContainerClass: "truncate",
    type: "widget",
    widgetName: "schoolContactWidget",
  },
  {
    columnName: "Actions",
    headerLabel: "Actions",
    sortable: false,
    columnClass:
      "w-full md:w-[12%] text-center md:bg-gray-200 md:py-2",
    cellClass: "w-full md:w-[12%]",
    type: "widget",
    widgetName: "schoolActions",
  },
];



export default function SchoolTable(props: SchoolTableProps) {
    return (
        <div
            className={cn(
                "w-full flex flex-col gap-4 md:gap-0 md:overflow-auto rounded border-box border",
                props.className,
            )}
        >
            <TableHeader
                metadata={SchoolMetadata}
                className="border-none  rounded-none md:px-0 md:py-0 md:gap-0   rounded-t  "
            />
            {props.data?.length ? (
                props.data.map((item: any, index: number) => {
                    const school={
                        id: item.school.id,
                        name: item.school.school_name,
                        address: item.school.address,
                        city: item.school.city,
                        state: item.school.state,
                        country: item.school.country,
                        zipcode: item.school.zipcode,
                        website: item.school.website,
                        phone: item.admin_user.mobile_no ?? "N/A",
                        email: item.admin_user.email,
                        status: item.admin_user.is_active ,
                        created_at: item.school.created_at,
                        admin_id: item.admin_user.id,
                        is_active:item.admin_user.is_active?"TRUE":"FALSE"

                    }
school.admin_id==38&&console.log("admi id 38 data :",school)
                    return (
                        <TableRow
                            key={index}
                            data={school}
                            metadata={SchoolMetadata}
                            className="w-full border-x-0 border-b-0 px-4 py-2 md:py-1 md:px-0 "
                        />
                    );
                })
            ) : (
                <div className="py-2 align-middle mx-auto">
                    <Label className="text-gray-500" variant="semibold">
                        No Candidates Found with matching Criteria
                    </Label>
                </div>
            )}
        </div>
    );
}