import React from "react";
import EditSchool from "./EditSchool";
import GoToSchoolLink from "./GoToSchoolLink";
import AssignTemplatesToSchoolDialog from "../AssignTemplatesToSchoolDialog";
import { Button } from "@/components/ui/button";

export default function SchoolActions({ schoolData }: { schoolData: any }) {
  return (
    <section className="w-full flex items-center md:justify-center gap-2 ">
      <GoToSchoolLink schoolData={schoolData} />
      <EditSchool schoolData={schoolData} editingSchool={true} />
      <AssignTemplatesToSchoolDialog schoolId={schoolData.id}>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 py-1 rounded-md border text-[#8D8EF5] hover:bg-[#8D8EF5]/10"
        >
          Assign Templates
        </Button>
      </AssignTemplatesToSchoolDialog>
    </section>
  );
}
