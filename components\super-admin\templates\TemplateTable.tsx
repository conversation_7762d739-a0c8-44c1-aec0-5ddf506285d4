import { FormMode } from "@/types";
import { Template } from "@/lib/superadmin/template";
import CreateAndUpdateTemplateDialog from "./CreateAndUpdateTemplateDialog";
import DeleteTemplateDialog from "./DeleteTemplateDialog";
import StatusSwitcher from "./StatusSwitcher";
import { fetchTemplates } from "@/lib/superadmin/template";

interface TemplateTableProps {
  templates: Template[];
}

export default async function TemplateTable({ templates }: TemplateTableProps) {
  const getTemplateTypeFromColor = (color: string) => {
    const normalizedColor = color.toLowerCase();
    
    if (normalizedColor === "0xff25b166") {
      return "Appreciation";
    } else if (normalizedColor === "0xffff9395") {
      return "Negative";
    }
    
    return "Custom";
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };
  const formattedData = await fetchTemplates( 0);

  return (
    <div className="shadow-xl rounded-b-xl border border-[#E0E0E0] overflow-hidden">
      <table className="w-full">
        <thead>
          <tr>
            <th className="text-left px-6 py-5 font-medium text-sm">Name</th>
            <th className="text-left px-4 py-5 font-medium text-sm">Type</th>
            <th className="text-left px-4 py-5 font-medium text-sm">Subject</th>
            <th className="text-left px-4 py-5 font-medium text-sm">Template Type</th>
            {/* <th className="text-left px-4 py-5 font-medium text-sm">Custom</th> */}
            <th className="text-left px-4 py-5 font-medium text-sm">Status</th>
            <th className="text-left px-4 py-5 font-medium text-sm">Created</th>
            <th className="text-left px-4 py-5 font-medium text-sm">Actions</th>
          </tr>
        </thead>
        <tbody>
          {templates.length === 0 ? (
            <tr>
              <td colSpan={8} className="text-center border-t py-4 text-muted-foreground">
                No templates found
              </td>
            </tr>
          ) : (
            templates.map((template: Template, index: number) => (
              <tr key={template.id} className={`last:border-b-0 ${index % 2 === 0 ? 'bg-[#F6F6FF]' : 'bg-white'}`}>
                <td className="px-6 py-4 font-medium">
                  {template.name}
                </td>
                <td className="p-4 capitalize">
                  {template.type === "staff" ? "Staff" : "Parent"}
                </td>
                <td className="p-4">
                  <div className="max-w-[200px]">
                    <div className="font-medium">{template.subject}</div>
                    <div className="text-sm text-gray-500 truncate" title={template.content}>
                      {template.content.substring(0, 50)}...
                    </div>
                  </div>
                </td>
                <td className="p-4">
                  {getTemplateTypeFromColor(template.color)}
                </td>
                <td className="p-4">
                  <StatusSwitcher 
                    templateId={template.id} 
                    isActive={template.is_active as boolean} 
                  />
                </td>
                <td className="p-4">
                  {formatDate(template.createdDate)}
                </td>
                <td className="p-4">
                  <div className="flex space-x-6">
                
                  <CreateAndUpdateTemplateDialog
                  mode={FormMode.UPDATE}
                  templateData={template}
                  />
                    <DeleteTemplateDialog templateId={template.id} templateName={template.name} />
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
} 