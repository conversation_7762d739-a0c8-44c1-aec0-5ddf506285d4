
import { makeApiRequest } from "@/lib/common/api-utils";

const apiUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/dashboard`;

export interface SubscriptionData {
  School: number[];
  Individual: number[];
}

export interface ActivityData {
  [key: string]: {
    active: number;
    inactive: number;
  };
}


export const fetchDashboardData = async () => {
  try {
    const profileUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/profile/`;
    const profileRes = await makeApiRequest(profileUrl, "GET");
    if (!profileRes.success) {
      throw new Error(String(profileRes.error));
    }
    const d = profileRes.data?.data ?? {};
    const totalEmail = Number(d.total_email_limit ?? 0);
    const usedEmail = Number(d.used_email_limit ?? 0);
    const totalSms = Number(d.total_sms_limit ?? 0);
    const usedSms = Number(d.used_sms_limit ?? 0);

    return {
      smsCount: usedSms,
      emailCount: usedEmail,
      emailLimit: totalEmail,
      smsLimit: totalSms,
      subscriptions: { School: [], Individual: [] },
      activity: { School: { active: 50, inactive: 50 }, Teachers: { active: 50 , inactive: 50 } },
    };
  } catch (e) {
    // Fallback to zeros on failure
    return {
      smsCount: 0,
      emailCount: 0,
      emailLimit: 0,
      smsLimit: 0,
      subscriptions: { School: [], Individual: [] },
      activity: { School: { active: 0, inactive: 0 }, Teachers: { active: 0, inactive: 0 } },
    };
  }
};



