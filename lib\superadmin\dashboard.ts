
import { QueryParameters } from "@/types/common";
import qs from "query-string";
import { getCookie } from "@/actions/cookie";
import { ICookies<PERSON>ey, Plan } from "@/types";

const apiUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/dashboard`;

export interface SubscriptionData {
  School: number[];
  Individual: number[];
}

export interface ActivityData {
  [key: string]: {
    active: number;
    inactive: number;
  };
}

export const useDemoData = true; // Toggle to false for production

export const fetchDashboardData = async () => {
  if (useDemoData) {
    return {
      smsCount: 12847,
      emailCount: 8524,
      subscriptions: {
        School: [342, 1247, 1247],
        Individual: [342, 1247, 1247],
      },
      activity: {
        School: { active: 47, inactive: 29 },
        Teachers: { active: 47, inactive: 29 },
      },
    };
  }

  // For now, return demo data
  return {
    smsCount: 12847,
    emailCount: 8524,
    subscriptions: {
      School: [342, 1247, 1247],
      Individual: [342, 1247, 1247],
    },
    activity: {
      School: { active: 47, inactive: 29 },
      Teachers: { active: 47, inactive: 29 },
    },
  };
};



