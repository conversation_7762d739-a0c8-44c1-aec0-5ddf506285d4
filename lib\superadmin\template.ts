import { paginationLimit } from '@/types';
import qs from 'query-string';
import { makeApiRequest } from '@/lib/common/api-utils';

interface TemplatesResponse {
  templates: Template[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    total_pages: number;
    next: null;
    previous: null;
  };
}

export interface Template {
  id: number
  name: string
  type: "staff" | "parent"
  subject: string
  content: string
  color: string
  createdDate: string
  is_custom: boolean
  is_active?: boolean
}

export interface TemplateFilterParams {
  template_type?: string;
  is_active?: string;
  is_custom?: string;
  user_id?: number;
  school_id?: number;
  name?: string;
  offset?: number;
  limit?: number;
}

export interface TemplatequeryParam {
  template_type?: string;
  is_active?: boolean;
  is_custom?: string;
  user_id?: number;
  school_id?: number;
  name?: string;
  offset?: number;
  limit?: number;
}

export async function fetchTemplates(
  offset: number = 0, 
  filters: TemplateFilterParams = {}
): Promise<TemplatesResponse> {
  try {
    const limit = paginationLimit.LIMIT_10;
    const API_URL = process.env.NEXT_PUBLIC_API_URL;
    
    // Build query parameters using the query builder
    const queryParams = buildTemplateQueryString({
      ...filters,
      limit: limit,
      offset,
    });
    
    const result = await makeApiRequest(
      `${API_URL}/api/v1/templates/get-all-web?${queryParams}`,
      'GET'
    );

    if (!result.success) {
      throw new Error(result.error ?? "Failed to fetch templates");
    }

    const data = result.data;

    // Handle "Template Not Found" response
    if (data.status === 200 && data.message === "Template Not Found") {
      return {
        templates: [],
        pagination: {
          total: 0,
          limit: limit,
          offset: offset,
          total_pages: 0,
          next: null,
          previous: null
        }
      };
    }

    if (data.status !== 200) {
      throw new Error(data.message ?? "Failed to fetch templates");
    }

    // Transform API data to match our Template interface
    const formattedTemplates: Template[] = data.data.result.map((item: any) => ({
      ...item,
      // normalize fields expected by UI
      subject: item.subject ?? item.template_subject ?? "",
      is_custom: Boolean(item.is_custom),
      type: String(item.type || "staff").toLowerCase(),
      color: item.template_color ? String(item.template_color).toLowerCase() : "black",
      createdDate: new Date(item.created_at).toISOString().split("T")[0],
    }));

    return {
      templates: formattedTemplates,
      pagination: {
        total: data?.data?.pagination?.total ?? formattedTemplates.length ?? 0,
        limit: limit,
        offset: offset,
        total_pages: Math.ceil((data?.data?.pagination?.total ?? formattedTemplates.length) / limit),
        next: null,
        previous: null
      }
    };
  } catch (error) {
    console.error('Error fetching templates:', error);
    return {
      templates: [],
      pagination: {
        total: 0,
        limit: paginationLimit.LIMIT_10,
        offset: offset,
        total_pages: 0,
        next: null,
        previous: null
      }
    };
  }
}

export const buildTemplateQueryString = (params: TemplateFilterParams) => {
  const queryParams: TemplatequeryParam = {};
  queryParams["name"] = params.name;
  queryParams["template_type"] = params.template_type;
  
  // Extract nested ternary into independent statement
  if (params.is_active) {
    queryParams["is_active"] = params.is_active === "true";
  }
  
  queryParams["is_custom"] = params.is_custom;
  queryParams["user_id"] = params.user_id;
  queryParams["school_id"] = params.school_id;
  queryParams["limit"] = params.limit;

  return qs.stringify(queryParams, {
    arrayFormat: "comma",
    skipNull: true,
    skipEmptyString: true,
    encode: false,
  });
};